extends Area2D

@export var mass: float = 650.0
@export var velocity: Vector2 = Vector2.ZERO

# Preload resources for performance
const BODY_SCENE = preload("res://Universe/body.tscn")
const TEX_ASTEROID = preload("res://assets/asteroid.png")
const TEX_MOON     = preload("res://assets/asteroid.png")
const TEX_PLANET   = preload("res://assets/asteroid.png")
const TEX_STAR     = preload("res://assets/asteroid.png")
const TEX_BLACKHOLE = preload("res://assets/asteroid.png")

@onready var collision_shape: CollisionShape2D = $CollisionShape2D
@onready var sprite: Sprite2D = $Sprite2D
var circle_shape: CircleShape2D

var force: Vector2 = Vector2.ZERO
var radius: float

func _ready():
	circle_shape = collision_shape.shape as CircleShape2D
	Universe.bodies.append(self)
	update_radius()
	update_visual()

func _physics_process(delta: float) -> void:
	force = Vector2.ZERO
	apply_gravity()
	move_self(delta)
	handle_collisions()

func apply_force(f: Vector2) -> void:
	force += f

func apply_gravity() -> void:
	# Calculate gravitational pull from all valid bodies
	for other in Universe.bodies:
		if other == self or not is_instance_valid(other):
			continue
		var dir = other.global_position - global_position
		var dist_sq = max(dir.length_squared(), Universe.MIN_DIST)
		var force_mag = Universe.G * mass * other.mass / dist_sq
		apply_force(dir.normalized() * force_mag)

func move_self(delta: float) -> void:
	var acc = force / mass
	velocity += acc * delta
	global_position += velocity * delta

func handle_collisions() -> void:
	# Check collisions against all valid bodies
	for other in Universe.bodies:
		if other == self or not is_instance_valid(other):
			continue
		var dist = global_position.distance_to(other.global_position)
		if dist < radius + other.radius:
			Universe.bodies.erase(self)
			Universe.bodies.erase(other)

			if (velocity - other.velocity).length() < 220:
				call_deferred("_perform_merge", other)
			else:
				call_deferred("_perform_fragment", other)
			return

func _perform_merge(other: Area2D) -> void:
	if not is_instance_valid(other):
		return
	var total_mass = mass + other.mass
	velocity = (velocity * mass + other.velocity * other.mass) / total_mass
	mass = total_mass
	update_radius()
	update_visual()
	other.queue_free()

func _perform_fragment(other: Area2D) -> void:
	if not is_instance_valid(other):
		return
	var per_mass = (mass + other.mass) / 6.0
	for i in range(3):
		var frag = BODY_SCENE.instantiate()
		frag.mass = per_mass
		frag.global_position = global_position
		frag.velocity = velocity + Vector2(
			randf_range(-50.0, 50.0),
			randf_range(-50.0, 50.0)
		)
		get_parent().add_child(frag)
		Universe.bodies.append(frag)

	queue_free()
	other.queue_free()

func update_radius() -> void:
	radius = clamp(sqrt(mass), 4.0, 80.0)
	(collision_shape.shape as CircleShape2D).radius = radius

func update_visual() -> void:
	var tex = TEX_ASTEROID
	if mass > 8000.0:
		tex = TEX_BLACKHOLE
	elif mass > 2000.0:
		tex = TEX_STAR
	elif mass > 500.0:
		tex = TEX_PLANET
	elif mass > 100.0:
		tex = TEX_MOON

	sprite.texture = tex
	sprite.scale = Vector2.ONE * radius / 32.0
